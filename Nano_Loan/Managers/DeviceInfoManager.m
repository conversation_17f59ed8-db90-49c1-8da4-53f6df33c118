#import "DeviceInfoManager.h"
#import <sys/sysctl.h>
#import <mach/mach.h>
#import <mach/mach_host.h>
#import <UIKit/UIKit.h>
#import <AdSupport/ASIdentifierManager.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <SystemConfiguration/CaptiveNetwork.h>
#import "AppDelegate.h"
#import <CoreTelephony/CTTelephonyNetworkInfo.h>

@implementation DeviceInfoManager

+ (NSDictionary *)collectDeviceInfo {
    NSMutableDictionary *result = [NSMutableDictionary dictionary];

    // 1. 存储信息
    NSError *fsErr;
    NSDictionary *fs = [[NSFileManager defaultManager] attributesOfFileSystemForPath:NSHomeDirectory() error:&fsErr];
    if (!fs) { fs = @{}; }
    unsigned long long totalSpace = [fs[NSFileSystemSize] unsignedLongLongValue];
    unsigned long long freeSpace = [fs[NSFileSystemFreeSize] unsignedLongLongValue];
    unsigned long long totalMemory = [[NSProcessInfo processInfo] physicalMemory];
    unsigned long long freeMemory = [self freeMemoryBytes];
    result[@"sliding"] = @{
        @"thefool": [NSString stringWithFormat:@"%llu", freeSpace],
        @"beenwhole": [NSString stringWithFormat:@"%llu", totalSpace],
        @"makings": [NSString stringWithFormat:@"%llu", totalMemory],
        @"learnto": [NSString stringWithFormat:@"%llu", freeMemory]
    };

    // 2. 电池
    UIDevice *device = [UIDevice currentDevice];
    device.batteryMonitoringEnabled = YES;
    int batteryPercent = device.batteryState == UIDeviceBatteryStateUnknown ? -1 : (int)(device.batteryLevel * 100);
    int charging = (device.batteryState == UIDeviceBatteryStateCharging || device.batteryState == UIDeviceBatteryStateFull) ? 1 : 0;
    result[@"grow"] = @{ @"job": @(batteryPercent), @"makingsof": @(charging) };

    // 3. 系统设备信息
    result[@"itdoesn"] = @{ @"teaching": device.systemVersion ?: @"",
                              @"doctoring": device.model ?: @"iPhone",
                              @"worthwhile": [self hardwareMachine] ?: @"" };

    // 4. 模拟器 / 越狱
    BOOL isSimulator = [self isSimulator];
    BOOL isJailbroken = [self isJailbroken];
    result[@"dozen"] = @{ @"dabbled": @(isSimulator), @"split": @(isJailbroken) };

    // 5. 其它信息
    NSString *timezone = [[NSTimeZone localTimeZone] name] ?: @"";
    NSString *language = [[NSLocale preferredLanguages] firstObject] ?: @"";
    NSString *networkType = [self networkTypeString];
    NSString *idfv = [[[UIDevice currentDevice] identifierForVendor] UUIDString] ?: @"";
    NSString *idfa = @"";
#if TARGET_OS_SIMULATOR
#else
    if (@available(iOS 14, *)) {
        if ([ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized) {
            idfa = [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString ?: @"";
        }
    } else {
        if ([ASIdentifierManager sharedManager].isAdvertisingTrackingEnabled) {
            idfa = [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString ?: @"";
        }
    }
#endif
    result[@"givingback"] = @{ @"surgeon": timezone,
                                 @"picnic": idfv,
                                 @"bigger": language,
                                 @"points": networkType,
                                 @"lunch": idfa };

    // 6. Wifi 信息
    NSDictionary *wifiInfo = [self currentWiFiInfo];
    result[@"thesame"] = @{ @"suffer": wifiInfo ?: @{} };

    return result;
}

#pragma mark - Helpers

+ (NSString *)hardwareMachine {
    size_t size;
    sysctlbyname("hw.machine", NULL, &size, NULL, 0);
    char *machine = malloc(size);
    sysctlbyname("hw.machine", machine, &size, NULL, 0);
    NSString *hardware = [NSString stringWithCString:machine encoding:NSUTF8StringEncoding];
    free(machine);
    return hardware;
}

+ (BOOL)isSimulator {
#if TARGET_OS_SIMULATOR
    return YES;
#else
    return NO;
#endif
}

+ (BOOL)isJailbroken {
#if TARGET_OS_SIMULATOR
    return NO;
#else
    NSArray *jailbreakPaths = @[@"/Applications/Cydia.app",
                               @"/Library/MobileSubstrate/MobileSubstrate.dylib",
                               @"/bin/bash",
                               @"/usr/sbin/sshd",
                               @"/etc/apt"];
    for (NSString *path in jailbreakPaths) {
        if ([[NSFileManager defaultManager] fileExistsAtPath:path]) {
            return YES;
        }
    }
    return NO;
#endif
}

+ (unsigned long long)freeMemoryBytes {
    mach_msg_type_number_t count = HOST_VM_INFO_COUNT;
    vm_statistics_data_t vmstat;
    kern_return_t kr = host_statistics(mach_host_self(), HOST_VM_INFO, (host_info_t)&vmstat, &count);
    if (kr != KERN_SUCCESS) return 0;
    unsigned long long free = (unsigned long long)vmstat.free_count * vm_page_size;
    return free;
}

+ (NSString *)networkTypeString {
    // 1. WiFi 检测，通过 SSID 获取是否连接 WiFi
    NSArray *ifs = CFBridgingRelease(CNCopySupportedInterfaces());
    for (NSString *ifnam in ifs) {
        NSDictionary *info = CFBridgingRelease(CNCopyCurrentNetworkInfo((__bridge CFStringRef)ifnam));
        if (info[@"SSID"]) {
            return @"WIFI";
        }
    }

    // 2. 蜂窝网络类型
    CTTelephonyNetworkInfo *teleInfo = [[CTTelephonyNetworkInfo alloc] init];
    NSString *radio = nil;
    if (@available(iOS 12.0, *)) {
        radio = teleInfo.serviceCurrentRadioAccessTechnology.allValues.firstObject;
    } else {
        radio = teleInfo.currentRadioAccessTechnology;
    }
    if (!radio) return @"OTHER";
    NSSet *set2G = [NSSet setWithObjects:CTRadioAccessTechnologyEdge, CTRadioAccessTechnologyGPRS, CTRadioAccessTechnologyCDMA1x, nil];
    NSSet *set3G = [NSSet setWithObjects:CTRadioAccessTechnologyWCDMA, CTRadioAccessTechnologyHSDPA, CTRadioAccessTechnologyHSUPA, CTRadioAccessTechnologyCDMAEVDORev0, CTRadioAccessTechnologyCDMAEVDORevA, CTRadioAccessTechnologyCDMAEVDORevB, CTRadioAccessTechnologyeHRPD, nil];
    NSSet *set4G = [NSSet setWithObjects:CTRadioAccessTechnologyLTE, nil];
    NSSet *set5G = nil;
    if (@available(iOS 14.1, *)) {
        set5G = [NSSet setWithObjects:CTRadioAccessTechnologyNRNSA, CTRadioAccessTechnologyNR, nil];
    }
    if ([set2G containsObject:radio]) return @"2G";
    if ([set3G containsObject:radio]) return @"3G";
    if ([set4G containsObject:radio]) return @"4G";
    if (set5G && [set5G containsObject:radio]) return @"5G";
    return @"OTHER";
}

+ (NSDictionary *)currentWiFiInfo {
    NSArray *ifs = CFBridgingRelease(CNCopySupportedInterfaces());
    for (NSString *ifnam in ifs) {
        NSDictionary *info = CFBridgingRelease(CNCopyCurrentNetworkInfo((__bridge CFStringRef)ifnam));
        if (info) {
            NSString *ssid = info[@"SSID"] ?: @"";
            NSString *bssid = info[@"BSSID"] ?: @"";
            return @{ @"finer": bssid, @"excitedbecause": ssid };
        }
    }
    return @{};
}

@end 
