//
//  CustomAddressPickerViewController.m
//  Nano_Loan
//
//  Created by iOSDev on 2025/06/30.
//

#import "CustomAddressPickerViewController.h"
#import "AddressNode.h"

// 定义屏幕适配比例和尺寸
#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height
#define SCALE_375 (SCREEN_WIDTH / 375.0)

typedef NS_ENUM(NSInteger, AddressLevel) {
    AddressLevelProvince = 0,
    AddressLevelCity = 1,
    AddressLevelDistrict = 2
};

@interface CustomAddressPickerViewController () <UITableViewDataSource, UITableViewDelegate, UIGestureRecognizerDelegate>

@property (nonatomic, copy) NSArray<AddressNode *> *roots;
@property (nonatomic, copy) CustomAddressPickerResult resultBlock;

// 容器视图
@property (nonatomic, strong) UIView *containerView;
// 选择栏（省市区切换）
@property (nonatomic, strong) UIView *segmentView;
@property (nonatomic, strong) UIButton *provinceButton;
@property (nonatomic, strong) UIButton *cityButton;
@property (nonatomic, strong) UIButton *districtButton;
@property (nonatomic, strong) UIImageView *indicatorView; // 黑色长条指示器

// 列表视图
@property (nonatomic, strong) UITableView *tableView;

// 数据状态
@property (nonatomic, strong) AddressNode *selectedProvince;
@property (nonatomic, strong) AddressNode *selectedCity;
@property (nonatomic, strong) AddressNode *selectedDistrict;
@property (nonatomic, assign) AddressLevel currentLevel;

@end

@implementation CustomAddressPickerViewController

#pragma mark - Initialization

- (instancetype)initWithAddressRoots:(NSArray<AddressNode *> *)roots
                           selection:(CustomAddressPickerResult)result {
    self = [super init];
    if (self) {
        _roots = roots;
        _resultBlock = [result copy];
        _currentLevel = AddressLevelProvince;
    }
    return self;
}

#pragma mark - View Lifecycle

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5]; // 半透明黑色背景
    
    // 创建容器和UI组件
    [self setupContainerView];
    [self setupHeaderView];
    [self setupSegmentView];
    [self setupTableView];
    [self setupBottomButton];
    
    // 默认选中省级
    [self updateCurrentLevel:AddressLevelProvince animated:NO];
    
    // 初始状态：透明度为0，位置在屏幕底部以外
    self.view.alpha = 0;
    self.containerView.transform = CGAffineTransformMakeTranslation(0, self.containerView.bounds.size.height);
    
    // 添加调试日志
    NSLog(@"CustomAddressPickerViewController viewDidLoad completed");
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    // 执行显示动画
    [UIView animateWithDuration:0.3 
                          delay:0.0 
                        options:UIViewAnimationOptionCurveEaseOut 
                     animations:^{
        self.view.alpha = 1.0;
        self.containerView.transform = CGAffineTransformIdentity;
    } completion:nil];
}

#pragma mark - UI Setup

- (void)setupContainerView {
    // 创建容器视图（使用photo_source_picker_bg背景）
    CGFloat height = 455.0 * SCALE_375; // 调整弹窗高度
    CGRect frame = CGRectMake(0, SCREEN_HEIGHT - height, SCREEN_WIDTH, height);
    
    self.containerView = [[UIView alloc] initWithFrame:frame];
    self.containerView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
    
    // 设置圆角
    self.containerView.layer.cornerRadius = 20.0;
    // 只圆角顶部
    self.containerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    self.containerView.layer.masksToBounds = YES;
    
    // 设置背景图
    UIImageView *bgImageView = [[UIImageView alloc] initWithFrame:self.containerView.bounds];
    bgImageView.image = [UIImage imageNamed:@"photo_source_picker_bg"];
    bgImageView.contentMode = UIViewContentModeScaleToFill;
    bgImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.containerView addSubview:bgImageView];
    
    [self.view addSubview:self.containerView];
    
    // 添加点击背景关闭的手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissByTapBackground:)];
    // 允许 containerView 内部控件继续接收触摸事件
    tapGesture.cancelsTouchesInView = NO;
    tapGesture.delegate = self;
    [self.view addGestureRecognizer:tapGesture];
}

- (void)setupHeaderView {
    // 标题和关闭按钮
    CGFloat headerHeight = 50.0;
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.containerView.bounds.size.width, headerHeight)];
    headerView.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    [self.containerView addSubview:headerView];
    
    // 标题
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Address";
    titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:18];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [headerView addSubview:titleLabel];
    
    // 标题位置调整：靠左距离30，上方距离11
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.leadingAnchor constraintEqualToAnchor:headerView.leadingAnchor constant:30],
        [titleLabel.topAnchor constraintEqualToAnchor:headerView.topAnchor constant:11]
    ]];
    
    // 关闭按钮
    UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeButton setImage:[UIImage imageNamed:@"photo_source_picker_close"] forState:UIControlStateNormal];
    closeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [closeButton addTarget:self action:@selector(dismissSelf) forControlEvents:UIControlEventTouchUpInside];
    [headerView addSubview:closeButton];
    
    // 关闭按钮右上角对齐
    [NSLayoutConstraint activateConstraints:@[
        [closeButton.topAnchor constraintEqualToAnchor:headerView.topAnchor constant:22],
        [closeButton.trailingAnchor constraintEqualToAnchor:headerView.trailingAnchor constant:-22],
        [closeButton.widthAnchor constraintEqualToConstant:30],
        [closeButton.heightAnchor constraintEqualToConstant:30]
    ]];
}

- (void)setupSegmentView {
    // 三个选择按钮（省、市、区）和指示器
    CGFloat segmentHeight = 50.0;
    CGFloat headerHeight = 50.0;
    CGFloat yPosition = headerHeight;
    
    self.segmentView = [[UIView alloc] initWithFrame:CGRectMake(0, yPosition, self.containerView.bounds.size.width, segmentHeight)];
    self.segmentView.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    [self.containerView addSubview:self.segmentView];
    
    // 三个按钮等分
    CGFloat buttonWidth = self.segmentView.bounds.size.width / 3.0;
    
    // 设置字体样式
    UIFont *buttonFont = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    
    // 省按钮
    self.provinceButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.provinceButton.frame = CGRectMake(0, 0, buttonWidth, segmentHeight);
    [self.provinceButton setTitle:@"Manila" forState:UIControlStateNormal];
    [self.provinceButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    self.provinceButton.titleLabel.font = buttonFont;
    [self.provinceButton addTarget:self action:@selector(selectProvince) forControlEvents:UIControlEventTouchUpInside];
    self.provinceButton.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleRightMargin;
    [self.segmentView addSubview:self.provinceButton];
    
    // 市按钮
    self.cityButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.cityButton.frame = CGRectMake(buttonWidth, 0, buttonWidth, segmentHeight);
    [self.cityButton setTitle:@"Select" forState:UIControlStateNormal];
    [self.cityButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    self.cityButton.titleLabel.font = buttonFont;
    [self.cityButton addTarget:self action:@selector(selectCity) forControlEvents:UIControlEventTouchUpInside];
    self.cityButton.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleLeftMargin | UIViewAutoresizingFlexibleRightMargin;
    [self.segmentView addSubview:self.cityButton];
    
    // 区按钮
    self.districtButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.districtButton.frame = CGRectMake(buttonWidth * 2, 0, buttonWidth, segmentHeight);
    [self.districtButton setTitle:@"Select" forState:UIControlStateNormal];
    [self.districtButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    self.districtButton.titleLabel.font = buttonFont;
    [self.districtButton addTarget:self action:@selector(selectDistrict) forControlEvents:UIControlEventTouchUpInside];
    self.districtButton.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleLeftMargin;
    [self.segmentView addSubview:self.districtButton];
    
    // 黑色长条指示器
    CGFloat indicatorWidth = 55.0; // 固定宽度为55pt
    CGFloat indicatorHeight = 4.0;
    // 计算居中位置
    CGFloat indicatorX = (buttonWidth - indicatorWidth) / 2.0;
    CGRect indicatorFrame = CGRectMake(indicatorX, segmentHeight - indicatorHeight, indicatorWidth, indicatorHeight);
    
    self.indicatorView = [[UIImageView alloc] initWithFrame:indicatorFrame];
    self.indicatorView.image = [UIImage imageNamed:@"Address_x_b"];
    self.indicatorView.contentMode = UIViewContentModeScaleToFill;
    [self.segmentView addSubview:self.indicatorView];
}

- (void)setupTableView {
    // 表格视图（显示当前级别的选项）
    CGFloat headerHeight = 50.0;
    CGFloat segmentHeight = 50.0;
    CGFloat bottomButtonHeight = 70.0; // 增加底部按钮区域高度，确保不遮挡内容
    CGFloat yPosition = headerHeight + segmentHeight;
    CGFloat tableViewHeight = self.containerView.bounds.size.height - yPosition - bottomButtonHeight;
    
    // 添加左右边距
    CGFloat horizontalMargin = 15.0;
    
    // 创建表格视图
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(
        horizontalMargin,
        yPosition,
        self.containerView.bounds.size.width - (horizontalMargin * 2),
        tableViewHeight)];
    
    tableView.delegate = self;
    tableView.dataSource = self;
    tableView.backgroundColor = [UIColor clearColor];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.rowHeight = 45.0;
    tableView.showsVerticalScrollIndicator = YES;
    // 禁止水平滚动，仅允许垂直方向滚动
    tableView.showsHorizontalScrollIndicator = NO;
    tableView.alwaysBounceHorizontal = NO;
    tableView.directionalLockEnabled = YES;
    tableView.userInteractionEnabled = YES; // 确保用户交互已启用
    
    // 确保表格视图在最上层
    [self.containerView addSubview:tableView];
    self.tableView = tableView;
    
    // 添加调试日志
    NSLog(@"TableView setup completed: %@", self.tableView);
}

- (void)setupBottomButton {
    // 底部确认按钮
    CGFloat buttonHeight = 50.0;
    // 确保按钮位置不会遮挡表格内容
    CGFloat yPosition = self.containerView.bounds.size.height - buttonHeight - 20; // 底部留出20pt边距
    
    UIButton *confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    confirmButton.frame = CGRectMake(40, yPosition, self.containerView.bounds.size.width - 80, buttonHeight);
    [confirmButton setTitle:@"Ok" forState:UIControlStateNormal];
    [confirmButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    confirmButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20];
    
    // 设置按钮背景图片
    [confirmButton setBackgroundImage:[UIImage imageNamed:@"face_guide_go_btn_bg"] forState:UIControlStateNormal];
    confirmButton.layer.cornerRadius = 25;
    confirmButton.layer.masksToBounds = YES;
    
    [confirmButton addTarget:self action:@selector(confirmSelection) forControlEvents:UIControlEventTouchUpInside];
    confirmButton.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
    [self.containerView addSubview:confirmButton];
}

#pragma mark - Actions

- (void)dismissByTapBackground:(UITapGestureRecognizer *)gesture {
    CGPoint location = [gesture locationInView:self.view];
    if (!CGRectContainsPoint(self.containerView.frame, location)) {
        [self dismissAnimated:YES];
    }
}

- (void)dismissSelf {
    [self dismissAnimated:YES];
}

- (void)dismissAnimated:(BOOL)animated {
    if (animated) {
        [UIView animateWithDuration:0.3 
                              delay:0.0 
                            options:UIViewAnimationOptionCurveEaseIn 
                         animations:^{
            self.view.alpha = 0;
            self.containerView.transform = CGAffineTransformMakeTranslation(0, self.containerView.bounds.size.height);
        } completion:^(BOOL finished) {
            [self dismissViewControllerAnimated:NO completion:nil];
        }];
    } else {
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

- (void)selectProvince {
    // 只切换到省级数据显示，不自动进入下一级
    [self updateCurrentLevel:AddressLevelProvince animated:YES];
}

- (void)selectCity {
    // 只有选择了省才能查看市级数据
    if (self.selectedProvince) {
        [self updateCurrentLevel:AddressLevelCity animated:YES];
    }
}

- (void)selectDistrict {
    // 只有选择了市才能查看区级数据
    if (self.selectedCity) {
        [self updateCurrentLevel:AddressLevelDistrict animated:YES];
    }
}

- (void)confirmSelection {
    // 需要至少选择到区级别才能确认
    if (self.selectedProvince && self.selectedCity && self.selectedDistrict) {
        if (self.resultBlock) {
            self.resultBlock(self.selectedProvince, self.selectedCity, self.selectedDistrict);
        }
        [self dismissAnimated:YES];
    } else {
        // 提示用户完成选择
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Please complete your selection" 
                                                                       message:@"Please select province, city and district" 
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:alert animated:YES completion:nil];
    }
}

#pragma mark - Level Management

- (void)updateCurrentLevel:(AddressLevel)level animated:(BOOL)animated {
    self.currentLevel = level;
    
    // 更新按钮状态
    [self updateButtonStates];
    
    // 更新指示器位置
    CGFloat buttonWidth = self.segmentView.bounds.size.width / 3.0;
    CGFloat indicatorWidth = 55.0; // 固定宽度
    
    // 计算指示器在当前按钮下方的居中位置
    CGFloat indicatorX = (buttonWidth - indicatorWidth) / 2.0 + (buttonWidth * level);
    
    CGRect indicatorFrame = self.indicatorView.frame;
    indicatorFrame.origin.x = indicatorX;
    
    if (animated) {
        [UIView animateWithDuration:0.25 animations:^{
            self.indicatorView.frame = indicatorFrame;
        }];
    } else {
        self.indicatorView.frame = indicatorFrame;
    }
    
    // 刷新表格数据
    [self.tableView reloadData];
}

- (void)updateButtonStates {
    // 根据选择情况更新按钮标题和状态
    if (self.selectedProvince) {
        [self.provinceButton setTitle:self.selectedProvince.name forState:UIControlStateNormal];
    } else {
        [self.provinceButton setTitle:@"Manila" forState:UIControlStateNormal];
    }
    
    if (self.selectedCity) {
        [self.cityButton setTitle:self.selectedCity.name forState:UIControlStateNormal];
    } else {
        [self.cityButton setTitle:@"Select" forState:UIControlStateNormal];
    }
    
    if (self.selectedDistrict) {
        [self.districtButton setTitle:self.selectedDistrict.name forState:UIControlStateNormal];
    } else {
        [self.districtButton setTitle:@"Select" forState:UIControlStateNormal];
    }
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSInteger count = 0;
    
    switch (self.currentLevel) {
        case AddressLevelProvince:
            count = self.roots.count;
            break;
        case AddressLevelCity:
            count = self.selectedProvince ? self.selectedProvince.children.count : 0;
            break;
        case AddressLevelDistrict:
            count = self.selectedCity ? self.selectedCity.children.count : 0;
            break;
    }
    
    NSLog(@"numberOfRowsInSection: %ld, level: %ld, count: %ld", (long)section, (long)self.currentLevel, (long)count);
    return count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    // 使用简单标识符
    static NSString *CellIdentifier = @"Cell";
    
    // 创建单元格
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
        cell.backgroundColor = [UIColor clearColor];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.textLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
        cell.textLabel.textAlignment = NSTextAlignmentLeft;
        
        // 确保单元格可以接收触摸事件
        cell.userInteractionEnabled = YES;
    }
    
    // 获取当前级别的节点
    AddressNode *node = [self nodeAtIndexPath:indexPath];
    if (node) {
        cell.textLabel.text = node.name;
    } else {
        cell.textLabel.text = @"Sampaloc"; // 默认显示样本文本
    }
    
    // 设置文本颜色
    cell.textLabel.textColor = [UIColor blackColor];
    
    // 检查是否是选中项
    BOOL isSelected = NO;
    if (node) {
        switch (self.currentLevel) {
            case AddressLevelProvince:
                isSelected = (self.selectedProvince && [node.name isEqualToString:self.selectedProvince.name]);
                break;
            case AddressLevelCity:
                isSelected = (self.selectedCity && [node.name isEqualToString:self.selectedCity.name]);
                break;
            case AddressLevelDistrict:
                isSelected = (self.selectedDistrict && [node.name isEqualToString:self.selectedDistrict.name]);
                break;
        }
    }
    
    // 设置选中项的背景
    if (isSelected) {
        UIImageView *bgView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"Address_Selected"]];
        bgView.contentMode = UIViewContentModeScaleToFill;
        cell.backgroundView = bgView;
    } else {
        cell.backgroundView = nil;
    }
    
    // 添加调试日志
    NSLog(@"Created cell for indexPath %@: %@, isSelected: %d", indexPath, cell, isSelected);
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 45.0; // 确保每个单元格高度为45pt
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    // 添加调试日志
    NSLog(@"Cell selected at indexPath: %@", indexPath);
    
    // 获取当前选中的节点
    AddressNode *node = [self nodeAtIndexPath:indexPath];
    if (!node) {
        NSLog(@"No node found at indexPath: %@", indexPath);
        return;
    }
    
    // 根据当前级别更新选择状态
    switch (self.currentLevel) {
        case AddressLevelProvince:
            NSLog(@"Selected province: %@", node.name);
            self.selectedProvince = node;
            self.selectedCity = nil;
            self.selectedDistrict = nil;
            // 更新按钮标题
            [self updateButtonStates];
            // 刷新当前列表，更新选中样式
            [tableView reloadData];
            break;
            
        case AddressLevelCity:
            NSLog(@"Selected city: %@", node.name);
            self.selectedCity = node;
            self.selectedDistrict = nil;
            [self updateButtonStates];
            [tableView reloadData];
            break;
            
        case AddressLevelDistrict:
            NSLog(@"Selected district: %@", node.name);
            self.selectedDistrict = node;
            [self updateButtonStates];
            [tableView reloadData];
            break;
    }
}

#pragma mark - Helper Methods

- (AddressNode *)nodeAtIndexPath:(NSIndexPath *)indexPath {
    AddressNode *node = nil;
    
    switch (self.currentLevel) {
        case AddressLevelProvince:
            if (indexPath.row < self.roots.count) {
                node = self.roots[indexPath.row];
            }
            break;
            
        case AddressLevelCity:
            if (self.selectedProvince && indexPath.row < self.selectedProvince.children.count) {
                node = self.selectedProvince.children[indexPath.row];
            }
            break;
            
        case AddressLevelDistrict:
            if (self.selectedCity && indexPath.row < self.selectedCity.children.count) {
                node = self.selectedCity.children[indexPath.row];
            }
            break;
    }
    
    NSLog(@"nodeAtIndexPath: %@, level: %ld, node: %@", indexPath, (long)self.currentLevel, node ? node.name : @"nil");
    return node;
}

#pragma mark - 禁止水平侧滑/编辑

#if __IPHONE_OS_VERSION_MAX_ALLOWED >= 110000
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath API_AVAILABLE(ios(11.0)) {
    // 返回 nil 表示不启用任何侧滑操作
    return nil;
}

- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView leadingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath API_AVAILABLE(ios(11.0)) {
    return nil;
}
#endif

// 禁止旧版 iOS 的编辑模式（避免左滑出现删除按钮）
- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath {
    return NO;
}

#pragma mark - UIGestureRecognizerDelegate

// 仅当触摸发生在 containerView 之外时才让手势生效，避免拦截 tableView 点击
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    CGPoint location = [touch locationInView:self.containerView];
    // 如果触摸点在 containerView 内，则不处理，让内部视图响应
    if (CGRectContainsPoint(self.containerView.bounds, location)) {
        return NO;
    }
    return YES;
}

@end 