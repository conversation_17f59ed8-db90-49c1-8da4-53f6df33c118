#import "LaunchViewController.h"
#import "NetworkManager.h"
#import "CustomTabBarController.h"
#import "LoginInitModel.h"
#import "AddressDataManager.h"
#import "LocationManager.h"
#import "AppDelegate.h"
#import <CFNetwork/CFNetwork.h>
#import <SystemConfiguration/SystemConfiguration.h>

// === Helper Functions ===
static BOOL NLIsProxyEnabled(void) {
    NSDictionary *proxySettings = (__bridge_transfer NSDictionary *)CFNetworkCopySystemProxySettings();
    if (!proxySettings) { return NO; }
    NSNumber *httpEnable = proxySettings[@"HTTPEnable"];
    NSNumber *httpsEnable = proxySettings[@"HTTPSEnable"];
    NSNumber *socksEnable = proxySettings[@"SOCKSEnable"];
    return (httpEnable.boolValue || httpsEnable.boolValue || socksEnable.boolValue);
}

static BOOL NLIsVPNEnabled(void) {
    NSDictionary *proxySettings = (__bridge_transfer NSDictionary *)CFNetworkCopySystemProxySettings();
    NSDictionary *scoped = proxySettings[@"__SCOPED__"];
    for (NSString *key in scoped.allKeys) {
        if ([key containsString:@"tap"] || [key containsString:@"tun"] || [key containsString:@"ppp"] || [key containsString:@"ipsec"] || [key containsString:@"utun"]) {
            return YES;
        }
    }
    return NO;
}

@implementation LaunchViewController

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    // 设置基础域名（真实接口）
    [NetworkManager setBaseURL:@"http://************:8330/youtoday"];
    // 启动全局定位
    [[LocationManager sharedManager] startUpdatingLocation];
    [self requestLoginInitWithRetry:NO];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 添加全屏启动背景图
    UIImageView *backgroundImageView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    backgroundImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    backgroundImageView.image = [UIImage imageNamed:@"launch_bg_image"];
    [self.view addSubview:backgroundImageView];
}

/// 发起登录初始化请求，支持重试一次
- (void)requestLoginInitWithRetry:(BOOL)hasRetried {
    __weak typeof(self) weakSelf = self;

    // ==== 构造初始化参数 ====
    NSString *langCode = [[NSLocale preferredLanguages] firstObject] ?: @"en";
    if (langCode.length > 2) {
        langCode = [[langCode substringToIndex:2] lowercaseString];
    }

    BOOL usingProxy = NLIsProxyEnabled();
    BOOL usingVPN = NLIsVPNEnabled();

    NSDictionary *initParams = @{@"bigger": langCode,
                                 @"ofgwendoline": @(usingProxy),
                                 @"outright": @(usingVPN)};

    [NetworkManager postFormWithAPI:@"Alicia/bigger" params:initParams completion:^(NSDictionary *response, NSError *error) {
        if (!error && [response isKindOfClass:[NSDictionary class]]) {
            // 保存数据到model
            LoginInitModel *model = [[LoginInitModel alloc] initWithDictionary:response];
            NSData *data = [NSKeyedArchiver archivedDataWithRootObject:model requiringSecureCoding:YES error:nil];
            [[NSUserDefaults standardUserDefaults] setObject:data forKey:@"LoginInitModelCache"];

            // ===== 缓存初始化数据供全局使用 =====
            // 保存完整 JSON 到 Documents/login_init_cache.json
            NSString *jsonPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject stringByAppendingPathComponent:@"login_init_cache.json"];
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:response options:0 error:nil];
            [jsonData writeToFile:jsonPath atomically:YES];

            // 提取手机国家区号 topmost 并缓存
            NSString *topmost = response[@"awkward"][@"heardmrs"][@"topmost"];
            if ([topmost isKindOfClass:[NSString class]] && topmost.length > 0) {
                [[NSUserDefaults standardUserDefaults] setObject:topmost forKey:@"country_phone_code"];
            }

            // ===== 缓存隐私协议 URL girlswere，供登录页使用 =====
            NSString *girlswere = response[@"awkward"][@"girlswere"];
            if ([girlswere isKindOfClass:[NSString class]] && girlswere.length > 0) {
                [[NSUserDefaults standardUserDefaults] setObject:girlswere forKey:@"protocol_url"];
            }
            // ===== 解析 everyonecheered 字段，供后续位置权限引导使用 =====
            NSNumber *everyonecheered = response[@"awkward"][@"everyonecheered"];
            if ([everyonecheered respondsToSelector:@selector(integerValue)]) {
                [[NSUserDefaults standardUserDefaults] setObject:everyonecheered forKey:@"show_location_guide"];
            }
            [[NSUserDefaults standardUserDefaults] synchronize];

            // === 使用 fearlessness 字段动态初始化 Facebook SDK ===
            NSDictionary *fbConfig = response[@"awkward"][@"fearlessness"];
            AppDelegate *appDelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
            if ([fbConfig isKindOfClass:[NSDictionary class]] && [appDelegate respondsToSelector:@selector(configureFacebookSDKWithParameters:)]) {
                [appDelegate configureFacebookSDKWithParameters:fbConfig];
            }

            // 判断 modest 字段并在主线程更新 UI
            dispatch_async(dispatch_get_main_queue(), ^{
                NSNumber *modest = response[@"modest"];
                if ([modest respondsToSelector:@selector(integerValue)] && [modest integerValue] == 0) {
                    // 进入首页
                    UIWindow *window = self.view.window;
                    if (!window) {
                        if (@available(iOS 13.0, *)) {
                            for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
                                if (scene.activationState == UISceneActivationStateForegroundActive) {
                                    window = scene.windows.firstObject;
                                    break;
                                }
                            }
                        } else {
                            window = [UIApplication sharedApplication].keyWindow;
                        }
                    }

                    if (window) {
                        CustomTabBarController *tabBarVC = [[CustomTabBarController alloc] init];
                        window.rootViewController = tabBarVC;
                        [UIView transitionWithView:window duration:0.3 options:UIViewAnimationOptionTransitionCrossDissolve animations:nil completion:^(BOOL finished) {
                            if (finished) {
                                // 预加载地址数据，方便后续选择器使用（异步执行，不阻塞UI）
                                [[AddressDataManager sharedManager] fetchAddressDataIfNeededWithCompletion:nil];
                            }
                        }];
                    } else {
                        NSLog(@"[Launch] ⚠️ 无法获取 Window，无法进入主页");
                    }
                } else {
                    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice" message:@"Service error, please try again later." preferredStyle:UIAlertControllerStyleAlert];
                    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
                    [weakSelf presentViewController:alert animated:YES completion:nil];
                }
            });
        } else {
            if (!hasRetried) {
                // 10秒后重试一次
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [weakSelf requestLoginInitWithRetry:YES];
                });
            } else {
                // 两次都失败，弹英文报错
                UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice" message:@"Network error, please check your connection and try again." preferredStyle:UIAlertControllerStyleAlert];
                [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
                [weakSelf presentViewController:alert animated:YES completion:nil];
            }
        }
    }];
}

@end

/*

{
  "modest": 0,
  "patted": "success",
  "awkward": {
      "roundwarily": 2,   //  1=默认印度(审核面)   2=菲律宾(用户面)  后续请求放在公参里面
      "heardmrs": {
          "topmost": "63", //手机区号
          "swallow": "http://2.412.163.251/national_flag/ph.png" //国旗logo
      },
  "everyonecheered": 1, //  1=弹出位置引导框   2=不弹 (场景：启动拒绝定位权限时，登陆后首页是否弹出位置引导弹窗，一天一次)
  "girlswere": "http://www.abidu.com", // 隐私协议
  "fearlessness": {
    // 【重要】FaceBook接入：http://47.238.207.2:3031/APP/FaceBook.git  账号：wendang  密码：wendang123
      "eventhe":"fb428921739874998",        // CFBundleURLScheme
      "gotback":"428921739874998",           // FacebookAppID
      "chicken":"PinoyLoan",    // FacebookDisplayName
      "ginger":"1a67e07ccdef7ad26a997dff1c5ab821"     // FacebookClientToke
      }
  }
}

*/
